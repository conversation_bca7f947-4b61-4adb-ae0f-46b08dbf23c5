{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#2c3e50">
    <link rel="manifest" href="{% static 'manifest.json' %}">
    <link rel="apple-touch-icon" href="{% static 'icons/icon-192x192.png' %}">
    <title>{% block title %}JW Expense Tracker{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar {
            background-color: #2c3e50 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: #ecf0f1 !important;
            font-weight: 600;
            font-size: 1.4rem;
            padding: 0.5rem 1rem;
        }
        .nav-link {
            color: #bdc3c7 !important;
            font-weight: 500;
            padding: 0.7rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            color: #ffffff !important;
            background-color: #34495e;
        }
        .nav-link.active {
            color: #ffffff !important;
            background-color: #3498db;
        }
        .navbar-toggler {
            border-color: #ecf0f1;
        }
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(236, 240, 241, 1)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        main {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .container {
            max-width: 1200px;
        }
    </style>
</head>
<body>
    {% include 'JW/includes/messages.html' %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'dashboard' %}">LTDWJ</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if 'students' in request.path %}active{% endif %}" href="{% url 'student_list' %}">Students</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'lessons' in request.path %}active{% endif %}" href="{% url 'lesson_list' %}">Lessons</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'tests' in request.path %}active{% endif %}" href="{% url 'tests_list' %}">Tests</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'business-expenses' in request.path %}active{% endif %}" href="{% url 'business_expense_list' %}">Expenses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'mileage_list' %}active{% endif %}" href="{% url 'mileage_list' %}">Mileage</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'fuel_expense_list' %}active{% endif %}" href="{% url 'fuel_expense_list' %}">Fuel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'manage_report_header' %}active{% endif %}" href="{% url 'manage_report_header' %}">My Data</a>
                    </li>
                    <li class="nav-item d-none d-xl-block">
                        <a class="nav-link {% if request.resolver_match.url_name == 'stats' %}active{% endif %}" href="{% url 'stats' %}">My Stats</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'report_list' %}active{% endif %}" href="{% url 'report_list' %}">Reports</a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin' in request.path %}active{% endif %}" href="{% url 'admin:index' %}">Admin</a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav ms-auto">
<!--                     <li class="nav-item">
                        <span class="nav-link">Welcome, {{ user.first_name }}</span>
                    </li> -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'logout' %}">Logout</a>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                {% if message.tags == 'error' %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block javascript %}{% endblock %}
</body>
</html>
