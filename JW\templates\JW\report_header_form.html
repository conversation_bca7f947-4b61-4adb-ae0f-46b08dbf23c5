{% extends 'JW/base.html' %}

{% block title %}My Data{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">My Data</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% for field_name, field_data in headers.items %}
                            <div class="row mb-3 align-items-center">
                                <div class="col-md-3">
                                    <label for="id_{{ field_name }}" class="form-label">{{ field_name|title }}</label>
                                </div>
                                <div class="col-md-7">
                                    <input type="text"
                                           name="{{ field_name }}"
                                           id="id_{{ field_name }}"
                                           value="{{ field_data.value }}"
                                           class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input type="checkbox"
                                               name="{{ field_name }}_enabled"
                                               id="id_{{ field_name }}_enabled"
                                               class="form-check-input"
                                               {% if field_data.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="id_{{ field_name }}_enabled">
                                            Show
                                        </label>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}

                        <!-- Tax/NI Percentage Section -->
                        <hr class="my-4">
                        <h5 class="mb-3">Tax & National Insurance Settings</h5>
                        <div class="row mb-3 align-items-center">
                            <div class="col-md-3">
                                <label for="{{ settings_form.tax_ni_percentage.id_for_label }}" class="form-label">{{ settings_form.tax_ni_percentage.label }}</label>
                            </div>
                            <div class="col-md-4">
                                {{ settings_form.tax_ni_percentage }}
                                {% if settings_form.tax_ni_percentage.errors %}
                                    <div class="text-danger small">
                                        {% for error in settings_form.tax_ni_percentage.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-5">
                                <small class="text-muted">
                                    This combined percentage gives a good estimate of Income Tax and NI deductions for earnings up to £50,000 per year in 2025
                                </small>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Test Centres Management Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Test Centres</h5>
                </div>
                <div class="card-body">
                    <!-- Add New Test Centre Form -->
                    <form method="post" class="mb-4">
                        {% csrf_token %}
                        <div class="row align-items-end">
                            <div class="col-md-8">
                                {{ test_centre_form.name.label_tag }}
                                {{ test_centre_form.name }}
                                {% if test_centre_form.name.errors %}
                                    <div class="text-danger small">
                                        {% for error in test_centre_form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <button type="submit" name="add_test_centre" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Test Centre
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Existing Test Centres List -->
                    {% if test_centres %}
                        <h6>Current Test Centres:</h6>
                        <div class="row">
                            {% for test_centre in test_centres %}
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                                        <span>{{ test_centre.name }}</span>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this test centre?');">
                                            {% csrf_token %}
                                            <input type="hidden" name="test_centre_id" value="{{ test_centre.id }}">
                                            <button type="submit" name="delete_test_centre" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No test centres added yet. Add your first test centre above.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
