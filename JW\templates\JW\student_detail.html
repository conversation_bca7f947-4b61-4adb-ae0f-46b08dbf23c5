{% extends 'JW/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Student Details - {{ student.student_name }}{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 800px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Student Details</h2>
        <div>
            <a href="{% url 'student_edit' student.id %}" class="btn btn-primary">Edit</a>
            <a href="{% url 'student_list' %}" class="btn btn-secondary">Back to List</a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Personal Information</h5>
                    <p><strong>Name:</strong> {{ student.student_name }}</p>
                    <p><strong>Mobile:</strong> {{ student.mobile_number|default:"Not provided" }}</p>
                    <p><strong>Email:</strong> {{ student.email_address|default:"Not provided" }}</p>
                    <p><strong>Gender:</strong> {{ student.gender|default:"Not specified" }}</p>
                    <p><strong>Age:</strong> {{ student.age|default:"Not specified" }}</p>
                </div>
                <div class="col-md-6">
                    <h5>Address</h5>
                    <p><strong>Address:</strong> {{ student.address_1st_line }}</p>
                    {% if student.address_2nd_line %}
                        <p>{{ student.address_2nd_line }}</p>
                    {% endif %}
                    <p><strong>Area:</strong> {{ student.area|default:"Not provided" }}</p>
                    <p><strong>Post Code:</strong> {{ student.post_code }}</p>
                    <p><strong>Status:</strong> {{ student.active }}</p>
                </div>
            </div>
            {% if student.notes %}
            <div class="row mt-3">
                <div class="col-12">
                    <h5>Notes</h5>
                    <p>{{ student.notes|linebreaks }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Student Statistics -->
            <div class="row mt-4">
                <div class="col-12">
                    <h5>Lesson Statistics</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <p><strong>Total Lessons:</strong> {{ student.lesson_count|default:"0" }}</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>Total Hours:</strong> {{ student.total_hours|default:"0"|floatformat:1 }}</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>Total Amount:</strong> £{{ student.total_amount|default:"0"|floatformat:2 }}</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>Last Lesson:</strong> {{ student.last_lesson|date:"d/m/Y"|default:"Never" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Credit Information -->
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Account Credit Information</h5>
            <a href="{% url 'student_edit' student.id %}" class="btn btn-sm btn-primary">Add Account Credit</a>
        </div>
        <div class="card-body">
            {% if block_bookings %}
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Amount Paid</th>
                            <th>Total Lessons</th>
                            <th>Lessons Remaining</th>
                            <th>Price/Lesson</th>
                            <th>Remainder</th>
                            <th>Method</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for booking in block_bookings %}
                        <tr>
                            <td>{{ booking.date_created|date:"M d, Y" }}</td>
                            <td>£{{ booking.amount_paid|floatformat:2 }}</td>
                            <td>{{ booking.total_lessons|floatformat:1 }}</td>
                            <td>{{ booking.lessons_remaining|floatformat:1 }} ({{ booking.effective_lessons_remaining|floatformat:1 }} effective)</td>
                            <td>£{{ booking.price_per_lesson|floatformat:2 }}</td>
                            <td>£{{ booking.remainder_balance|floatformat:2 }}</td>
                            <td>{{ booking.get_calculation_method_display }}</td>
                            <td>{% if booking.is_fully_used %}<span class="badge bg-secondary">Used</span>{% else %}<span class="badge bg-success">Active</span>{% endif %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Account Credit Usage History -->
            {% if block_booking_usage %}
            <h6 class="mt-4">Account Credit Usage History</h6>
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Lesson Hours</th>
                            <th>Credit Account</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for usage in block_booking_usage %}
                        <tr>
                            <td>{{ usage.date_used|date:"M d, Y" }}</td>
                            <td>{{ usage.lesson.lesson_hours|floatformat:1 }}</td>
                            <td>Credit account from {{ usage.block_booking.date_created|date:"M d, Y" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}

            {% else %}
            <p class="text-muted">No account credit found for this student.</p>
            {% endif %}
        </div>
    </div>

    <!-- Link to student's lessons -->
    <div class="mt-4">
        <a href="{% url 'student_lessons' student_name=student.student_name %}" class="btn btn-info">
            View Lesson History
        </a>
    </div>
</div>
{% endblock %}
