{% extends 'JW/base.html' %}

{% block title %}Tests{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <h2 class="mb-4">Test Results</h2>
    
    <!-- Passed Tests Section -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-check-circle"></i> Passed Tests ({{ passed_tests|length }})
            </h5>
        </div>
        <div class="card-body">
            {% if passed_tests %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th style="width: 25%">Student Name</th>
                                <th style="width: 20%">Test Date</th>
                                <th style="width: 25%">Test Centre</th>
                                <th style="width: 15%">Outcome</th>
                                <th style="width: 15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in passed_tests %}
                            <tr>
                                <td>
                                    <a href="{% url 'student_detail' student.pk %}" class="text-decoration-none">
                                        {{ student.student_name }}
                                    </a>
                                </td>
                                <td>{{ student.test_past|date:"M d, Y" }}</td>
                                <td>
                                    {% if student.test_centre %}
                                        {{ student.test_centre.name }}
                                    {% else %}
                                        <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-success">Passed</span>
                                </td>
                                <td>
                                    <a href="{% url 'test_edit' student.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit Details
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">No passed tests recorded yet.</p>
            {% endif %}
        </div>
    </div>

    <!-- Failed Tests Section -->
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i> Failed Tests ({{ failed_tests|length }})
            </h5>
        </div>
        <div class="card-body">
            {% if failed_tests %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th style="width: 25%">Student Name</th>
                                <th style="width: 20%">Test Date</th>
                                <th style="width: 25%">Test Centre</th>
                                <th style="width: 15%">Outcome</th>
                                <th style="width: 15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for failed_test in failed_tests %}
                            <tr>
                                <td>
                                    <a href="{% url 'student_lessons' student_name=failed_test.student_name %}" class="text-decoration-none">
                                        {{ failed_test.student_name }}
                                    </a>
                                </td>
                                <td>{{ failed_test.test_date|date:"M d, Y" }}</td>
                                <td>
                                    {% if failed_test.test_centre %}
                                        {{ failed_test.test_centre.name }}
                                    {% else %}
                                        <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-danger">Failed</span>
                                </td>
                                <td>
                                    <a href="{% url 'test_edit' failed_test.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit Details
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">No failed tests recorded yet.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
