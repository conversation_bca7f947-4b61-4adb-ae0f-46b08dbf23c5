from django import forms
from .models import (
    AppJwLesson, AppJwStudent, AppJwBusinessexpense,
    AppJwBusinessmileage, AppJwPersonalmileage,
    AppJwMileage, AppJwFuelexpense, AppJwBlockBooking,
    AppJwBlockBookingUsage, AppJwUserSettings, AppJwTestCentre
)

class LessonForm(forms.ModelForm):
    class Meta:
        model = AppJwLesson
        fields = [
            'date',
            'day_of_week',
            'student_name',
            'lesson_hours',
            'price_per_hour',
            'notes'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'student_name': forms.TextInput(attrs={
                'class': 'form-control',
                'list': 'student-list',
                'required': True
            }),
            'lesson_hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'price_per_hour': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_student_name(self):
        student_name = self.cleaned_data.get('student_name')
        if not student_name:
            raise forms.ValidationError("Student name is required")

        # Strict validation - must be an active student
        exists = AppJwStudent.objects.filter(
            student_name=student_name,
            active='Yes'
        ).exists()
        if not exists:
            raise forms.ValidationError(
                "Please select an active student from the list. "
                "If this is a new student, please add them first."
            )
        return student_name

    def clean(self):
        cleaned_data = super().clean()
        lesson_hours = cleaned_data.get('lesson_hours')
        price_per_hour = cleaned_data.get('price_per_hour')
        action = self.data.get('action')  # Get the form action

        errors = {}

        # Only validate non-zero values if not a test result action
        if action not in ['passed', 'failed']:
            if lesson_hours is not None:
                if lesson_hours < 0:
                    errors['lesson_hours'] = "Lesson hours cannot be negative"
                elif lesson_hours == 0:
                    errors['lesson_hours'] = "Lesson hours must be greater than 0"

            if price_per_hour is not None:
                if price_per_hour < 0:
                    errors['price_per_hour'] = "Price per hour cannot be negative"
                elif price_per_hour == 0:
                    errors['price_per_hour'] = "Price per hour must be greater than 0"

        if errors:
            raise forms.ValidationError(errors)

        return cleaned_data

class StudentForm(forms.ModelForm):
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
        ('Other', 'Other'),
    ]

    gender = forms.ChoiceField(
        choices=GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    class Meta:
        model = AppJwStudent
        fields = [
            'student_name',
            'gender',
            'age',
            'email_address',
            'mobile_number',
            'area',
            'address_1st_line',
            'address_2nd_line',
            'post_code',
            'active',
            'notes'
        ]
        widgets = {
            'student_name': forms.TextInput(attrs={'class': 'form-control'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'age': forms.NumberInput(attrs={'class': 'form-control'}),
            'email_address': forms.EmailInput(attrs={'class': 'form-control'}),
            'mobile_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. 07123456789 or +44 7123 456789'
            }),
            'area': forms.TextInput(attrs={'class': 'form-control', 'list': 'area-list'}),
            'address_1st_line': forms.TextInput(attrs={'class': 'form-control'}),
            'address_2nd_line': forms.TextInput(attrs={'class': 'form-control'}),
            'post_code': forms.TextInput(attrs={'class': 'form-control'}),
            'active': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean_mobile_number(self):
        mobile = self.cleaned_data.get('mobile_number')
        if mobile:
            mobile = mobile.strip()
            # Remove all non-digit characters for validation
            digits_only = ''.join(filter(str.isdigit, mobile))

            # Check if it's a valid UK mobile number
            if len(digits_only) < 10:
                raise forms.ValidationError(
                    "Mobile number must be at least 10 digits long. "
                    "Please enter a valid UK mobile number (e.g. 07123456789)."
                )
            elif len(digits_only) > 15:
                raise forms.ValidationError(
                    "Mobile number is too long. Please enter a valid mobile number."
                )

            # For UK numbers, check if it starts with 07 or +447
            if mobile.startswith('+44'):
                if len(digits_only) != 13 or not digits_only.startswith('447'):
                    raise forms.ValidationError(
                        "Invalid UK mobile number format. Use +44 7123 456789 format."
                    )
            elif mobile.startswith('07'):
                if len(digits_only) != 11:
                    raise forms.ValidationError(
                        "Invalid UK mobile number format. Use 07123456789 format (11 digits)."
                    )
            elif digits_only.startswith('7') and len(digits_only) == 10:
                # Allow 7123456789 format (without leading 0)
                pass
            elif mobile.startswith('0') and len(digits_only) == 11:
                # Allow other 0-prefixed numbers like landlines
                pass
            else:
                raise forms.ValidationError(
                    "Please enter a valid UK mobile number starting with 07 or +44 7."
                )

        return mobile

    def clean_student_name(self):
        name = self.cleaned_data.get('student_name')
        if name:
            name = name.strip()
            if len(name) < 2:
                raise forms.ValidationError("Student name must be at least 2 characters long.")
            if len(name) > 30:
                raise forms.ValidationError("Student name cannot exceed 30 characters.")
        return name

    def clean_email_address(self):
        email = self.cleaned_data.get('email_address')
        if email:
            email = email.strip().lower()
            if len(email) > 100:
                raise forms.ValidationError("Email address cannot exceed 100 characters.")
        return email

    def clean_age(self):
        age = self.cleaned_data.get('age')
        if age is not None:
            if age < 16:
                raise forms.ValidationError("Student must be at least 16 years old.")
            if age > 120:
                raise forms.ValidationError("Please enter a valid age.")
        return age

class BusinessExpenseForm(forms.ModelForm):
    class Meta:
        model = AppJwBusinessexpense
        fields = ['date', 'day_of_week', 'expense_type', 'cost', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'expense_type': forms.TextInput(attrs={
                'class': 'form-control',
                'list': 'expense-type-list',
                'required': True
            }),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_expense_type(self):
        expense_type = self.cleaned_data.get('expense_type')
        if not expense_type:
            raise forms.ValidationError("Expense type is required")
        if len(expense_type) > 20:  # Based on model field length
            raise forms.ValidationError("Expense type cannot exceed 20 characters")
        return expense_type.strip()

    def clean_cost(self):
        cost = self.cleaned_data.get('cost')
        if cost is None:
            raise forms.ValidationError("Cost is required")
        if cost < 0:
            raise forms.ValidationError("Cost cannot be negative")
        if cost == 0:
            raise forms.ValidationError("Cost must be greater than 0")
        return cost

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]

        return cleaned_data

class BusinessMileageForm(forms.ModelForm):
    class Meta:
        model = AppJwBusinessmileage
        fields = ['date', 'day_of_week', 'mileage', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_mileage(self):
        mileage = self.cleaned_data.get('mileage')
        if mileage is None:
            raise forms.ValidationError("Mileage is required")
        if mileage < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if mileage == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return mileage

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class PersonalMileageForm(forms.ModelForm):
    class Meta:
        model = AppJwPersonalmileage
        fields = ['date', 'day_of_week', 'mileage', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_mileage(self):
        mileage = self.cleaned_data.get('mileage')
        if mileage is None:
            raise forms.ValidationError("Mileage is required")
        if mileage < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if mileage == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return mileage

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class MileageForm(forms.ModelForm):
    class Meta:
        model = AppJwMileage
        fields = ['date', 'day', 'miles', 'mileage_type', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage_type': forms.Select(attrs={'class': 'form-control'}),
            'miles': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_miles(self):
        miles = self.cleaned_data.get('miles')
        if miles is None:
            raise forms.ValidationError("Mileage is required")
        if miles < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if miles == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return miles

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class FuelExpenseForm(forms.ModelForm):
    class Meta:
        model = AppJwFuelexpense
        fields = ['date', 'cost', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_cost(self):
        cost = self.cleaned_data.get('cost')
        if cost is None:
            raise forms.ValidationError("Cost is required")
        if cost < 0:
            raise forms.ValidationError("Cost cannot be negative")
        if cost == 0:
            raise forms.ValidationError("Cost must be greater than 0")
        return cost

    def clean_date(self):
        date = self.cleaned_data.get('date')
        if not date:
            raise forms.ValidationError("Date is required")
        return date

class ReportHeaderForm(forms.Form):
    name = forms.CharField(max_length=255, required=False)
    name_enabled = forms.BooleanField(required=False)

    address = forms.CharField(max_length=255, required=False)
    address_enabled = forms.BooleanField(required=False)

    phone = forms.CharField(max_length=255, required=False)
    phone_enabled = forms.BooleanField(required=False)

    reference = forms.CharField(max_length=255, required=False)
    reference_enabled = forms.BooleanField(required=False)

    other = forms.CharField(max_length=255, required=False)
    other_enabled = forms.BooleanField(required=False)

class BlockBookingForm(forms.ModelForm):
    class Meta:
        model = AppJwBlockBooking
        fields = [
            'date_created',
            'amount_paid',
            'total_lessons',
            'price_per_lesson_fixed',
            'remainder_balance',
            'calculation_method',
            'notes'
        ]
        widgets = {
            'date_created': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'amount_paid': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'total_lessons': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'price_per_lesson_fixed': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'remainder_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'calculation_method': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean(self):
        cleaned_data = super().clean()
        amount_paid = cleaned_data.get('amount_paid')
        total_lessons = cleaned_data.get('total_lessons')
        price_per_lesson_fixed = cleaned_data.get('price_per_lesson_fixed')
        remainder_balance = cleaned_data.get('remainder_balance')
        calculation_method = cleaned_data.get('calculation_method')
        date_created = cleaned_data.get('date_created')

        errors = {}

        if not date_created:
            errors['date_created'] = "Date is required"

        if amount_paid is None:
            errors['amount_paid'] = "Amount paid is required"
        elif amount_paid <= 0:
            errors['amount_paid'] = "Amount paid must be greater than 0"

        if total_lessons is None:
            errors['total_lessons'] = "Total lessons is required"
        elif total_lessons <= 0:
            errors['total_lessons'] = "Total lessons must be greater than 0"

        if calculation_method == 'new':
            if price_per_lesson_fixed is None:
                errors['price_per_lesson_fixed'] = "Price per lesson (fixed) is required for 'New' calculation method."
            elif price_per_lesson_fixed <= 0:
                errors['price_per_lesson_fixed'] = "Price per lesson (fixed) must be greater than 0."
            # remainder_balance can be negative (overpayment), so we don't validate it as non-negative
        else: # legacy method
            # For legacy method, ensure price_per_lesson_fixed is not set
            if price_per_lesson_fixed is not None:
                cleaned_data['price_per_lesson_fixed'] = None
            # remainder_balance should be 0 for legacy method
            cleaned_data['remainder_balance'] = 0

        if errors:
            raise forms.ValidationError(errors)

        return cleaned_data

class UserSettingsForm(forms.ModelForm):
    class Meta:
        model = AppJwUserSettings
        fields = ['tax_ni_percentage']
        widgets = {
            'tax_ni_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
        }
        labels = {
            'tax_ni_percentage': 'Tax/NI Percentage (%)',
        }

    def clean_tax_ni_percentage(self):
        percentage = self.cleaned_data.get('tax_ni_percentage')
        if percentage is None:
            raise forms.ValidationError("Tax/NI percentage is required")
        if percentage < 0:
            raise forms.ValidationError("Tax/NI percentage cannot be negative")
        if percentage > 100:
            raise forms.ValidationError("Tax/NI percentage cannot be greater than 100")
        return percentage

class TestCentreForm(forms.ModelForm):
    class Meta:
        model = AppJwTestCentre
        fields = ['name']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter test centre name (e.g., Ashford, Canterbury)',
                'maxlength': 100
            }),
        }
        labels = {
            'name': 'Test Centre Name',
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError("Test centre name is required")

        # Check for duplicates (case-insensitive)
        existing = AppJwTestCentre.objects.filter(
            name__iexact=name,
            active=True
        )

        # If editing, exclude the current instance
        if self.instance and self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)

        if existing.exists():
            raise forms.ValidationError("A test centre with this name already exists")

        return name.strip().title()  # Clean and capitalize properly
